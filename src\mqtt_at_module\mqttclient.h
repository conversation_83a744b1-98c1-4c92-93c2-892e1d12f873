#ifndef MQTTCLIENT_H
#define MQTTCLIENT_H

#include <QObject>
#include <QString>
#include <QMap>
#include <QTimer>
#include <QLoggingCategory>
#include <QtMqtt/QMqttClient>
#include <QtMqtt/QMqttTopicName>

Q_DECLARE_LOGGING_CATEGORY(mqttClientLog)

/**
 * @brief 单个MQTT客户端实现，支持AT指令接口
 *
 * 该类管理单个MQTT客户端实例的生命周期，
 * 响应AT指令并上报URC事件。
 * 兼容EC20等模块的MQTT行为。
 */
class MqttClient : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 客户端连接状态
     */
    enum State {
        IDLE,           ///< 初始状态
        NET_READY,      ///< AT+QMTOPEN完成，网络已配置
        CONNECTING,     ///< AT+QMTCONN进行中
        CONNECTED,      ///< MQTT连接已建立
        DISCONNECTING   ///< 断开连接进行中
    };

    /**
     * @brief 构造函数
     * @param clientIdx 客户端索引 (0-5)
     * @param parent 父对象
     */
    explicit MqttClient(int clientIdx, QObject *parent = nullptr);

    /**
     * @brief 析构函数
     */
    ~MqttClient();

    // === AT指令处理接口 ===

    /**
     * @brief 打开网络连接 (AT+QMTOPEN)
     * @param hostname MQTT服务器主机名或IP
     * @param port MQTT服务器端口
     * @return 如果指令被接受返回true，错误时返回false
     */
    bool openNetwork(const QString& hostname, int port);

    /**
     * @brief 连接到MQTT代理 (AT+QMTCONN)
     * @param clientId MQTT客户端标识符
     * @param username 可选的认证用户名
     * @param password 可选的认证密码
     * @return 如果指令被接受返回true，错误时返回false
     */
    bool connectMqtt(const QString& clientId,
                     const QString& username = QString(),
                     const QString& password = QString());

    /**
     * @brief 订阅主题 (AT+QMTSUB)
     * @param msgId MCU提供的消息ID
     * @param topic 要订阅的主题
     * @param qos 服务质量等级
     * @return 如果指令被接受返回true，错误时返回false
     */
    bool subscribe(int msgId, const QString& topic, int qos);

    /**
     * @brief 发布消息 (AT+QMTPUBEX)
     * @param msgId MCU提供的消息ID
     * @param qos 服务质量等级
     * @param retain 保留标志
     * @param topic 发布的主题
     * @param payload 消息载荷
     * @return 如果指令被接受返回true，错误时返回false
     */
    bool publish(int msgId, int qos, bool retain,
                 const QString& topic, const QByteArray& payload);

    /**
     * @brief 从MQTT代理断开连接 (AT+QMTDISC)
     * @return 如果指令被接受返回true，错误时返回false
     */
    bool disconnect();

    /**
     * @brief 关闭网络连接 (AT+QMTCLOSE)
     * @return 如果指令被接受返回true，错误时返回false
     */
    bool closeNetwork();

    // === 状态查询接口 ===

    /**
     * @brief 检查网络是否已打开 (AT+QMTOPEN?)
     * @return 如果网络已配置返回true
     */
    bool isNetworkOpen() const;

    /**
     * @brief 获取MQTT连接状态 (AT+QMTCONN?)
     * @return 连接状态 (1=初始化, 2=连接中, 3=已连接, 4=断开中)
     */
    int getConnectionState() const;

    /**
     * @brief 获取查询响应的网络信息
     * @return 格式化的网络信息字符串
     */
    QString getNetworkInfo() const;

signals:
    // === URC事件信号 ===

    /**
     * @brief 网络打开结果 (+QMTOPEN URC)
     * @param clientIdx 客户端索引
     * @param result 结果代码 (0=成功)
     */
    void networkOpened(int clientIdx, int result);

    /**
     * @brief MQTT连接结果 (+QMTCONN URC)
     * @param clientIdx 客户端索引
     * @param result 结果代码 (0=成功)
     * @param retCode MQTT返回代码
     */
    void mqttConnected(int clientIdx, int result, int retCode);

    /**
     * @brief 订阅结果 (+QMTSUB URC)
     * @param clientIdx 客户端索引
     * @param msgId 来自AT指令的消息ID
     * @param result 结果代码 (0=成功)
     * @param qos 授予的QoS等级
     */
    void subscribed(int clientIdx, int msgId, int result, int qos);

    /**
     * @brief 发布结果 (+QMTPUBEX URC)
     * @param clientIdx 客户端索引
     * @param msgId 来自AT指令的消息ID
     * @param result 结果代码 (0=成功)
     */
    void published(int clientIdx, int msgId, int result);

    /**
     * @brief MQTT断开连接结果 (+QMTDISC URC)
     * @param clientIdx 客户端索引
     * @param result 结果代码 (0=成功)
     */
    void mqttDisconnected(int clientIdx, int result);

    /**
     * @brief 网络关闭结果 (+QMTCLOSE URC)
     * @param clientIdx 客户端索引
     * @param result 结果代码 (0=成功)
     */
    void networkClosed(int clientIdx, int result);

    /**
     * @brief 接收到消息 (+QMTRECV URC)
     * @param clientIdx 客户端索引
     * @param msgId 消息ID
     * @param topic 主题名称
     * @param payload 消息载荷
     */
    void messageReceived(int clientIdx, int msgId, const QString& topic,
                        const QByteArray& payload);

    /**
     * @brief 状态变化通知 (+QMTSTAT URC)
     * @param clientIdx 客户端索引
     * @param errorCode 指示状态变化原因的错误代码
     */
    void statusChanged(int clientIdx, int errorCode);

private slots:
    // === QMqttClient事件处理槽 ===
    void onMqttConnected();
    void onMqttDisconnected();
    void onMqttError(QMqttClient::ClientError error);
    void onMessageReceived(const QByteArray& message, const QMqttTopicName& topic);

private:
    // === 内部辅助函数 ===
    void setState(State newState);
    void emitMqttConnectedAsync(int result, int retCode);

    // 基本标识
    int m_clientIdx;                    ///< 客户端索引 (0-5)

    // 连接信息
    QString m_hostname;                 ///< MQTT服务器主机名
    int m_port;                        ///< MQTT服务器端口
    QString m_clientId;                ///< MQTT客户端标识符
    QString m_username;                ///< 认证用户名
    QString m_password;                ///< 认证密码

    // 状态管理
    State m_state;                     ///< 当前客户端状态

    // MQTT实例
    QMqttClient* m_mqttClient;         ///< 实际的MQTT客户端实例

    // 订阅管理
    QMap<QString, int> m_subscriptions; ///< 主题 -> QoS映射

};

#endif // MQTTCLIENT_H
