#ifndef MQTTCLIENTMANAGER_H
#define MQTTCLIENTMANAGER_H

#include <QObject>
#include <QMap>
#include <QString>
#include <QLoggingCategory>
#include "mqttclient.h"

Q_DECLARE_LOGGING_CATEGORY(mqttClientManagerLog)

/**
 * @brief MQTT客户端管理器
 * 
 * 管理6个MQTT客户端实例(0-5)，处理AT指令路由和状态查询
 * 遵循EC20模块行为，支持多客户端并发操作
 */
class MqttClientManager : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param parent 父对象
     */
    explicit MqttClientManager(QObject *parent = nullptr);
    
    /**
     * @brief 析构函数
     */
    ~MqttClientManager();

    // === AT指令处理接口 ===
    
    /**
     * @brief 处理AT+QMTOPEN指令
     * @param clientIdx 客户端索引(0-5)
     * @param hostname MQTT服务器地址
     * @param port MQTT服务器端口
     * @return true=指令接受，false=参数错误
     */
    bool handleQmtOpen(int clientIdx, const QString& hostname, int port);
    
    /**
     * @brief 处理AT+QMTCONN指令
     * @param clientIdx 客户端索引(0-5)
     * @param clientId MQTT客户端ID
     * @param username 用户名(可选)
     * @param password 密码(可选)
     * @return true=指令接受，false=参数错误
     */
    bool handleQmtConn(int clientIdx, const QString& clientId, 
                       const QString& username = QString(), 
                       const QString& password = QString());
    
    /**
     * @brief 处理AT+QMTSUB指令
     * @param clientIdx 客户端索引(0-5)
     * @param msgId 消息ID(由MCU提供)
     * @param topic 订阅主题
     * @param qos QoS等级
     * @return true=指令接受，false=参数错误
     */
    bool handleQmtSub(int clientIdx, int msgId, const QString& topic, int qos);
    
    /**
     * @brief 处理AT+QMTPUBEX指令
     * @param clientIdx 客户端索引(0-5)
     * @param msgId 消息ID(由MCU提供)
     * @param qos QoS等级
     * @param retain 保留标志
     * @param topic 发布主题
     * @param payload 消息载荷
     * @return true=指令接受，false=参数错误
     */
    bool handleQmtPubex(int clientIdx, int msgId, int qos, bool retain,
                        const QString& topic, const QByteArray& payload);
    
    /**
     * @brief 处理AT+QMTDISC指令
     * @param clientIdx 客户端索引(0-5)
     * @return true=指令接受，false=参数错误
     */
    bool handleQmtDisc(int clientIdx);
    
    /**
     * @brief 处理AT+QMTCLOSE指令
     * @param clientIdx 客户端索引(0-5)
     * @return true=指令接受，false=参数错误
     */
    bool handleQmtClose(int clientIdx);

    // === 状态查询接口 ===
    
    /**
     * @brief 处理AT+QMTOPEN?查询
     * @return 格式化的网络状态信息
     */
    QString handleQmtOpenQuery() const;
    
    /**
     * @brief 处理AT+QMTCONN?查询
     * @return 格式化的连接状态信息
     */
    QString handleQmtConnQuery() const;

signals:
    // === URC事件信号(转发客户端事件) ===
    
    /**
     * @brief 网络连接完成事件
     * @param clientIdx 客户端索引
     * @param result 结果码(0=成功)
     */
    void networkOpened(int clientIdx, int result);
    
    /**
     * @brief MQTT连接完成事件
     * @param clientIdx 客户端索引
     * @param result 结果码(0=成功)
     * @param retCode MQTT返回码
     */
    void mqttConnected(int clientIdx, int result, int retCode);
    
    /**
     * @brief 订阅完成事件
     * @param clientIdx 客户端索引
     * @param msgId 消息ID
     * @param result 结果码(0=成功)
     * @param qos 授予的QoS等级
     */
    void subscribed(int clientIdx, int msgId, int result, int qos);
    
    /**
     * @brief 发布完成事件
     * @param clientIdx 客户端索引
     * @param msgId 消息ID
     * @param result 结果码(0=成功)
     */
    void published(int clientIdx, int msgId, int result);
    
    /**
     * @brief MQTT断开完成事件
     * @param clientIdx 客户端索引
     * @param result 结果码(0=成功)
     */
    void mqttDisconnected(int clientIdx, int result);
    
    /**
     * @brief 网络关闭完成事件
     * @param clientIdx 客户端索引
     * @param result 结果码(0=成功)
     */
    void networkClosed(int clientIdx, int result);
    
    /**
     * @brief 消息接收事件
     * @param clientIdx 客户端索引
     * @param msgId 消息ID
     * @param topic 主题名称
     * @param payload 消息载荷
     */
    void messageReceived(int clientIdx, int msgId, const QString& topic, 
                        const QByteArray& payload);
    
    /**
     * @brief 状态变化事件
     * @param clientIdx 客户端索引
     * @param errorCode 错误码
     */
    void statusChanged(int clientIdx, int errorCode);

private slots:
    // === 客户端事件处理槽 ===
    void onClientNetworkOpened(int clientIdx, int result);
    void onClientMqttConnected(int clientIdx, int result, int retCode);
    void onClientSubscribed(int clientIdx, int msgId, int result, int qos);
    void onClientPublished(int clientIdx, int msgId, int result);
    void onClientMqttDisconnected(int clientIdx, int result);
    void onClientNetworkClosed(int clientIdx, int result);
    void onClientMessageReceived(int clientIdx, int msgId, const QString& topic, 
                                const QByteArray& payload);
    void onClientStatusChanged(int clientIdx, int errorCode);

private:
    // === 内部辅助函数 ===
    
    /**
     * @brief 创建指定索引的客户端实例（按需创建）
     * @param clientIdx 客户端索引
     * @return 创建的客户端指针，失败返回nullptr
     */
    MqttClient* createClient(int clientIdx);

    /**
     * @brief 销毁指定索引的客户端实例
     * @param clientIdx 客户端索引
     * @return true=成功销毁，false=客户端不存在或索引无效
     */
    bool destroyClient(int clientIdx);
    
    /**
     * @brief 获取指定索引的客户端
     * @param clientIdx 客户端索引
     * @return 客户端指针，无效索引返回nullptr
     */
    MqttClient* getClient(int clientIdx) const;
    
    /**
     * @brief 验证客户端索引有效性
     * @param clientIdx 客户端索引
     * @return true=有效(0-5)，false=无效
     */
    bool isValidClientIndex(int clientIdx) const;
    
    /**
     * @brief 连接客户端信号到管理器槽
     * @param client 客户端实例
     */
    void connectClientSignals(MqttClient* client);

    // === 成员变量 ===
    
    /**
     * @brief 客户端实例映射表
     * key: 客户端索引(0-5)
     * value: MqttClient实例指针
     */
    QMap<int, MqttClient*> m_clients;
    
    /**
     * @brief 最大客户端数量
     */
    static const int MAX_CLIENTS = 6;
};

#endif // MQTTCLIENTMANAGER_H
